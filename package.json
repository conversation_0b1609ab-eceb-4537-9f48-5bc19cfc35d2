{"name": "sku-query-service", "version": "1.0.0", "description": "A simple SKU query service using Node.js and Express to forward requests to the OZON API.", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.1.3", "express": "^4.18.2", "https-proxy-agent": "^7.0.6", "package.json": "^2.0.1", "socks-proxy-agent": "^8.0.5"}, "devDependencies": {"nodemon": "^2.0.20"}}