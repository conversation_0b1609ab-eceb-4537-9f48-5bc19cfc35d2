const axios = require('axios');

// 测试代理轮询和按IP管理cookie的功能
async function testProxyRotation() {
    const baseUrl = 'http://localhost:3000';
    
    console.log('=== 测试代理轮询和按IP Cookie管理 ===\n');
    
    try {
        // 1. 测试获取当前cookie状态
        console.log('1. 获取当前cookie状态:');
        const cookieResponse = await axios.get(`${baseUrl}/api/get-cookie`);
        console.log('Cookie状态:', cookieResponse.data);
        console.log('');
        
        // 2. 为不同IP设置不同的cookie
        console.log('2. 为不同IP设置cookie:');
        
        // 为第一个IP设置cookie
        await axios.post(`${baseUrl}/api/update-cookie`, {
            ip: '***************:1080',
            cookie: 'test_cookie_ip1=value1; sc_company_id=2749342'
        });
        console.log('已为 ***************:1080 设置cookie');
        
        // 为第二个IP设置cookie
        await axios.post(`${baseUrl}/api/update-cookie`, {
            ip: '***************:1080',
            cookie: 'test_cookie_ip2=value2; sc_company_id=2749342'
        });
        console.log('已为 ***************:1080 设置cookie');
        
        // 设置默认cookie
        await axios.post(`${baseUrl}/api/update-cookie`, {
            ip: 'default',
            cookie: 'test_cookie_default=default_value; sc_company_id=2749342'
        });
        console.log('已设置默认cookie');
        console.log('');
        
        // 3. 验证不同IP的cookie
        console.log('3. 验证不同IP的cookie:');
        
        const ip1Cookie = await axios.get(`${baseUrl}/api/get-cookie?ip=***************:1080`);
        console.log('IP1 cookie:', ip1Cookie.data.cookie);
        
        const ip2Cookie = await axios.get(`${baseUrl}/api/get-cookie?ip=***************:1080`);
        console.log('IP2 cookie:', ip2Cookie.data.cookie);
        
        const defaultCookie = await axios.get(`${baseUrl}/api/get-cookie?ip=default`);
        console.log('Default cookie:', defaultCookie.data.cookie);
        console.log('');
        
        // 4. 查看所有cookie
        console.log('4. 所有cookie状态:');
        const allCookies = await axios.get(`${baseUrl}/api/get-cookie`);
        console.log('所有cookies:', JSON.stringify(allCookies.data.allCookies, null, 2));
        
        console.log('\n=== 测试完成 ===');
        
    } catch (error) {
        console.error('测试失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
if (require.main === module) {
    testProxyRotation();
}

module.exports = { testProxyRotation };
